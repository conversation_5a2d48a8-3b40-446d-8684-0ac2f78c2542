import React, { useState, useEffect } from 'react';

interface AITool {
    name: string;
    displayName: string;
    url: string;
    category: string;
}

interface AIToolsPanelProps {
    isVisible: boolean;
    onClose: () => void;
}

const AIToolsPanel: React.FC<AIToolsPanelProps> = ({ isVisible, onClose }) => {
    const [selectedCategory, setSelectedCategory] = useState<string>('all');

    // Définition de TOUS les outils LLM avec leurs liens
    const allAITools: AITool[] = [
        // LLM & Agents Conversationnels
        { name: 'CHATGPT', displayName: 'ChatGPT', url: 'https://chat.openai.com', category: 'LLM' },
        { name: 'GEMINI', displayName: 'Gemini', url: 'https://gemini.google.com', category: 'LLM' },
        { name: 'CLAUDE', displayName: 'Claude', url: 'https://claude.ai', category: 'LLM' },
        { name: '<PERSON>AM<PERSON>', displayName: 'Llama', url: 'https://ai.meta.com/llama/', category: 'LLM' },
        { name: 'MISTRAL', displayName: 'Mistral AI', url: 'https://mistral.ai/', category: 'LLM' },
        { name: 'LECHAT', displayName: 'Le Chat', url: 'https://chat.mistral.ai', category: 'LLM' },
        { name: 'PERPLEXITY', displayName: 'Perplexity AI', url: 'https://www.perplexity.ai', category: 'LLM' },
        { name: 'GROK', displayName: 'Grok', url: 'https://x.ai/grok', category: 'LLM' },
        { name: 'COHERE', displayName: 'Cohere', url: 'https://cohere.com/', category: 'LLM' },
        { name: 'HUGGINGCHAT', displayName: 'Hugging Chat', url: 'https://huggingface.co/chat', category: 'LLM' },
        { name: 'KIMI', displayName: 'Kimi', url: 'https://kimi.moonshot.cn/', category: 'LLM' },
        { name: 'YI', displayName: 'Yi', url: 'https://01.ai/', category: 'LLM' },
        { name: 'JASPER', displayName: 'Jasper', url: 'https://www.jasper.ai', category: 'LLM' },
        { name: 'COPYAI', displayName: 'Copy.ai', url: 'https://www.copy.ai', category: 'LLM' },
        { name: 'YOUCOM', displayName: 'You.com', url: 'https://you.com', category: 'LLM' },

        // Génération d'Images
        { name: 'MIDJOURNEY', displayName: 'Midjourney', url: 'https://www.midjourney.com', category: 'Images' },
        { name: 'DALLE', displayName: 'DALL-E 3', url: 'https://openai.com/dall-e-3', category: 'Images' },
        { name: 'FIREFLY', displayName: 'Adobe Firefly', url: 'https://www.adobe.com/sensei/generative-ai/firefly.html', category: 'Images' },
        { name: 'FREEPIK', displayName: 'Freepik AI', url: 'https://www.freepik.com/ai', category: 'Images' },

        // Génération de Vidéo & Musique
        { name: 'SUNO', displayName: 'Suno', url: 'https://suno.com', category: 'Média' },
        { name: 'PIKA', displayName: 'Pika', url: 'https://pika.art', category: 'Média' },
        { name: 'RUNWAY', displayName: 'RunwayML', url: 'https://runwayml.com', category: 'Média' },
        { name: 'HEYGEN', displayName: 'HeyGen', url: 'https://www.heygen.com', category: 'Média' },

        // Assistants de Code & Outils Développeur
        { name: 'COPILOT', displayName: 'GitHub Copilot', url: 'https://github.com/features/copilot', category: 'Code' },
        { name: 'GEMINICODE', displayName: 'Gemini Code Assist', url: 'https://cloud.google.com/products/gemini/code-assist', category: 'Code' },
        { name: 'AUGMENT', displayName: 'Augment Code', url: 'https://www.augmentcode.com/', category: 'Code' },
        { name: 'CODEWHISPERER', displayName: 'Amazon CodeWhisperer', url: 'https://aws.amazon.com/codewhisperer/', category: 'Code' },
        { name: 'TABNINE', displayName: 'Tabnine', url: 'https://www.tabnine.com', category: 'Code' },
        { name: 'CODIUM', displayName: 'CodiumAI', url: 'https://www.codium.ai/', category: 'Code' },
        { name: 'CODELLAMA', displayName: 'Code Llama', url: 'https://ai.meta.com/blog/code-llama/', category: 'Code' },
        { name: 'REFACT', displayName: 'Refact.ai', url: 'https://refact.ai', category: 'Code' },
        { name: 'LITELLM', displayName: 'LiteLLM', url: 'https://www.litellm.ai', category: 'Code' },
        { name: 'OPENROUTER', displayName: 'OpenRouter', url: 'https://openrouter.ai', category: 'Code' },

        // Productivité & Automatisation
        { name: 'NOTION', displayName: 'Notion AI', url: 'https://www.notion.so/product/ai', category: 'Productivité' },
        { name: 'FIREFLIES', displayName: 'Fireflies.ai', url: 'https://fireflies.ai', category: 'Productivité' },
        { name: 'AQUAVOICE', displayName: 'Aqua Voice', url: 'https://www.aquavoice.app/', category: 'Productivité' },
        { name: 'ABACUS', displayName: 'Abacus.ai', url: 'https://abacus.ai/', category: 'Productivité' },
        { name: 'MINDSTUDIO', displayName: 'MindStudio', url: 'https://www.mindstudio.ai', category: 'Productivité' },

        // Design & Présentations
        { name: 'GAMMA', displayName: 'Gamma', url: 'https://gamma.app', category: 'Design' },
        { name: 'TOME', displayName: 'Tome', url: 'https://tome.app', category: 'Design' },
        { name: 'UIZARD', displayName: 'Uizard', url: 'https://uizard.io', category: 'Design' }
    ];

    const categories = ['all', 'LLM', 'Images', 'Média', 'Code', 'Productivité', 'Design'];

    const categoryColors: { [key: string]: string } = {
        'all': '#FFFFFF',
        'LLM': '#2190F6',
        'Images': '#6689EF', 
        'Média': '#8D86ED',
        'Code': '#AE87F3',
        'Productivité': '#D65E65',
        'Design': '#1A3452'
    };

    const filteredTools = selectedCategory === 'all' 
        ? allAITools 
        : allAITools.filter(tool => tool.category === selectedCategory);

    const handleToolClick = (url: string) => {
        window.open(url, '_blank', 'noopener,noreferrer');
    };

    // Fermer le panneau avec Escape et gérer le scroll maître
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        if (isVisible) {
            // Bloquer le scroll du body (scroll devient esclave)
            document.body.classList.add('ai-panel-open');
            document.addEventListener('keydown', handleEscape);
        } else {
            // Restaurer le scroll du body (scroll redevient maître)
            document.body.classList.remove('ai-panel-open');
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.classList.remove('ai-panel-open');
        };
    }, [isVisible, onClose]);

    if (!isVisible) return null;

    return (
        <>
            {/* Styles globaux pour le scroll invisible */}
            <style>{`
                .ai-tools-panel .scrollbar-hide {
                    -ms-overflow-style: none;
                    scrollbar-width: none;
                    overflow-y: scroll !important;
                    overflow-x: hidden;
                }
                .ai-tools-panel .scrollbar-hide::-webkit-scrollbar {
                    display: none;
                    width: 0px;
                    background: transparent;
                }
                .ai-tools-panel .tools-container {
                    height: calc(100vh - 300px);
                    overflow-y: scroll;
                    overflow-x: hidden;
                    overscroll-behavior: contain;
                }
                /* Bloquer le scroll du body quand le panneau est ouvert */
                body.ai-panel-open {
                    overflow: hidden;
                }
            `}</style>

            <div className="ai-tools-panel fixed inset-0 z-50 flex">
                {/* Overlay sombre */}
                <div
                    className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
                    onClick={onClose}
                />
            
            {/* Panneau latéral */}
            <div className="relative ml-auto w-96 h-full bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 shadow-2xl transform transition-transform duration-300 ease-out">
                {/* En-tête avec gradient Gemini */}
                <div className="p-6 bg-gradient-to-r from-blue-900 via-blue-700 to-purple-700 border-b border-gray-700">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-white">
                            🌟 Outils IA & LLM
                        </h2>
                        <button
                            onClick={onClose}
                            className="text-white hover:text-red-400 transition-colors text-2xl"
                        >
                            ✕
                        </button>
                    </div>
                    <p className="text-blue-200 text-sm mt-2">
                        Explorez les meilleurs assistants IA
                    </p>
                </div>

                {/* Filtres par catégorie */}
                <div className="p-4 border-b border-gray-700">
                    <div className="flex flex-wrap gap-2">
                        {categories.map(category => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`px-3 py-1 rounded-full text-xs font-medium transition-all ${
                                    selectedCategory === category
                                        ? 'bg-blue-600 text-white shadow-lg'
                                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                }`}
                                style={{
                                    borderColor: selectedCategory === category ? categoryColors[category] : 'transparent',
                                    borderWidth: '1px'
                                }}
                            >
                                {category === 'all' ? 'Tous' : category}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Liste des outils avec scroll invisible */}
                <div className="tools-container scrollbar-hide p-4 space-y-2">
                    {filteredTools.map(tool => (
                        <div
                            key={tool.name}
                            onClick={() => handleToolClick(tool.url)}
                            className="group p-3 bg-gray-800 hover:bg-gray-700 rounded-lg cursor-pointer transition-all duration-200 border border-gray-700 hover:border-blue-500"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-white font-medium group-hover:text-blue-400 transition-colors">
                                        {tool.displayName}
                                    </h3>
                                    <p className="text-gray-400 text-xs">
                                        {tool.category}
                                    </p>
                                </div>
                                <div className="text-gray-500 group-hover:text-blue-400 transition-colors">
                                    🔗
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Footer */}
                <div className="p-4 border-t border-gray-700 bg-gray-900">
                    <p className="text-gray-400 text-xs text-center">
                        Cliquez sur un outil pour le visiter • ESC pour fermer
                    </p>
                </div>
            </div>
        </div>
        </>
    );
};

export default AIToolsPanel;
